#!/bin/bash

# 测试录音时间显示问题的脚本

echo "=== 测试 tinyalsa 录音时间显示问题 ==="

# 编译程序
echo "编译 tinyalsa_record..."
gcc -o tinyalsa_record tinyalsa_record.c -ltinyalsa -lm

if [ $? -ne 0 ]; then
    echo "编译失败，请检查 tinyalsa 库是否安装"
    exit 1
fi

echo "编译成功"

# 测试短时间录音
echo ""
echo "=== 测试1: 录音5秒 ==="
./tinyalsa_record -t 5 -f /tmp/test_5sec.wav -D

if [ -f /tmp/test_5sec.wav ]; then
    echo ""
    echo "检查录音文件信息:"
    ls -lh /tmp/test_5sec.wav
    
    # 使用 file 命令检查文件信息
    echo ""
    echo "文件类型信息:"
    file /tmp/test_5sec.wav
    
    # 使用 hexdump 检查WAV头部
    echo ""
    echo "WAV文件头部信息 (前44字节):"
    hexdump -C /tmp/test_5sec.wav | head -3
    
    # 计算预期文件大小
    echo ""
    echo "预期计算:"
    echo "  5秒 × 48000Hz × 2通道 × 4字节 = $(( 5 * 48000 * 2 * 4 )) 字节数据"
    echo "  加上44字节WAV头 = $(( 5 * 48000 * 2 * 4 + 44 )) 字节总大小"
    
    actual_size=$(stat -c%s /tmp/test_5sec.wav)
    echo "  实际文件大小: $actual_size 字节"
    
    expected_size=$(( 5 * 48000 * 2 * 4 + 44 ))
    if [ $actual_size -eq $expected_size ]; then
        echo "  ✓ 文件大小正确"
    else
        echo "  ✗ 文件大小不匹配，差异: $(( actual_size - expected_size )) 字节"
    fi
else
    echo "录音文件未生成"
fi

echo ""
echo "=== 测试2: 录音3秒 ==="
./tinyalsa_record -t 3 -f /tmp/test_3sec.wav -D

if [ -f /tmp/test_3sec.wav ]; then
    echo ""
    echo "检查录音文件信息:"
    ls -lh /tmp/test_3sec.wav
    
    actual_size=$(stat -c%s /tmp/test_3sec.wav)
    expected_size=$(( 3 * 48000 * 2 * 4 + 44 ))
    echo "  实际文件大小: $actual_size 字节"
    echo "  预期文件大小: $expected_size 字节"
    
    if [ $actual_size -eq $expected_size ]; then
        echo "  ✓ 文件大小正确"
    else
        echo "  ✗ 文件大小不匹配，差异: $(( actual_size - expected_size )) 字节"
    fi
fi

echo ""
echo "=== 测试完成 ==="
echo "如果文件大小匹配，说明录音时间计算正确"
echo "如果播放器显示时间仍然不对，可能是播放器解析WAV头的问题"

# 清理测试文件
echo ""
echo "清理测试文件..."
rm -f /tmp/test_5sec.wav /tmp/test_3sec.wav

echo "测试脚本执行完成"
