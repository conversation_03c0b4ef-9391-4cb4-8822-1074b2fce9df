#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>

// WAV文件头结构
typedef struct {
    char riff[4];           // "RIFF"
    uint32_t chunk_size;    // 文件大小 - 8
    char wave[4];           // "WAVE"
    char fmt[4];            // "fmt "
    uint32_t fmt_size;      // fmt chunk 大小 (16)
    uint16_t audio_format;  // 音频格式 (1 = PCM)
    uint16_t num_channels;  // 通道数
    uint32_t sample_rate;   // 采样率
    uint32_t byte_rate;     // 字节率
    uint16_t block_align;   // 块对齐
    uint16_t bits_per_sample; // 位深
    char data[4];           // "data"
    uint32_t data_size;     // 数据大小
} wav_header_t;

int main(int argc, char *argv[]) {
    if (argc != 2) {
        printf("用法: %s <wav文件>\n", argv[0]);
        return 1;
    }
    
    const char *filename = argv[1];
    FILE *fp = fopen(filename, "rb");
    if (!fp) {
        printf("无法打开文件: %s\n", filename);
        return 1;
    }
    
    // 获取文件大小
    fseek(fp, 0, SEEK_END);
    long file_size = ftell(fp);
    fseek(fp, 0, SEEK_SET);
    
    // 读取WAV头
    wav_header_t header;
    if (fread(&header, sizeof(header), 1, fp) != 1) {
        printf("读取WAV头失败\n");
        fclose(fp);
        return 1;
    }
    
    fclose(fp);
    
    // 分析WAV文件
    printf("=== WAV文件分析: %s ===\n", filename);
    printf("文件大小: %ld 字节\n", file_size);
    printf("\n");
    
    printf("WAV头信息:\n");
    printf("  RIFF标识: %.4s\n", header.riff);
    printf("  Chunk大小: %u 字节\n", header.chunk_size);
    printf("  WAVE标识: %.4s\n", header.wave);
    printf("  FMT标识: %.4s\n", header.fmt);
    printf("  FMT大小: %u\n", header.fmt_size);
    printf("  音频格式: %u (1=PCM)\n", header.audio_format);
    printf("  通道数: %u\n", header.num_channels);
    printf("  采样率: %u Hz\n", header.sample_rate);
    printf("  字节率: %u bytes/sec\n", header.byte_rate);
    printf("  块对齐: %u bytes\n", header.block_align);
    printf("  位深: %u bits\n", header.bits_per_sample);
    printf("  DATA标识: %.4s\n", header.data);
    printf("  数据大小: %u 字节\n", header.data_size);
    printf("\n");
    
    // 计算时长
    float duration_from_header = (float)header.data_size / header.byte_rate;
    float duration_from_file = (float)(file_size - sizeof(wav_header_t)) / header.byte_rate;
    
    printf("时长计算:\n");
    printf("  根据头部数据: %.3f 秒\n", duration_from_header);
    printf("  根据文件大小: %.3f 秒\n", duration_from_file);
    printf("\n");
    
    // 验证一致性
    printf("一致性检查:\n");
    
    // 检查chunk_size
    uint32_t expected_chunk_size = file_size - 8;
    if (header.chunk_size == expected_chunk_size) {
        printf("  ✓ Chunk大小正确\n");
    } else {
        printf("  ✗ Chunk大小错误: 期望 %u, 实际 %u\n", 
               expected_chunk_size, header.chunk_size);
    }
    
    // 检查data_size
    uint32_t expected_data_size = file_size - sizeof(wav_header_t);
    if (header.data_size == expected_data_size) {
        printf("  ✓ 数据大小正确\n");
    } else {
        printf("  ✗ 数据大小错误: 期望 %u, 实际 %u\n", 
               expected_data_size, header.data_size);
    }
    
    // 检查字节率计算
    uint32_t expected_byte_rate = header.sample_rate * header.num_channels * (header.bits_per_sample / 8);
    if (header.byte_rate == expected_byte_rate) {
        printf("  ✓ 字节率计算正确\n");
    } else {
        printf("  ✗ 字节率计算错误: 期望 %u, 实际 %u\n", 
               expected_byte_rate, header.byte_rate);
    }
    
    // 检查块对齐
    uint16_t expected_block_align = header.num_channels * (header.bits_per_sample / 8);
    if (header.block_align == expected_block_align) {
        printf("  ✓ 块对齐正确\n");
    } else {
        printf("  ✗ 块对齐错误: 期望 %u, 实际 %u\n", 
               expected_block_align, header.block_align);
    }
    
    printf("\n");
    
    // 总结
    if (header.chunk_size == expected_chunk_size && 
        header.data_size == expected_data_size &&
        header.byte_rate == expected_byte_rate &&
        header.block_align == expected_block_align) {
        printf("结论: WAV文件格式正确，时长应为 %.3f 秒\n", duration_from_header);
    } else {
        printf("结论: WAV文件头存在错误，可能导致播放器显示错误时长\n");
    }
    
    return 0;
}
