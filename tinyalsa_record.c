#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <unistd.h>
#include <errno.h>
#include <signal.h>
#include <getopt.h>
#include <time.h>
#include <math.h>
#include <tinyalsa/asoundlib.h>

// WAV文件头结构
typedef struct {
    char riff[4];           // "RIFF"
    uint32_t chunk_size;    // 文件大小 - 8
    char wave[4];           // "WAVE"
    char fmt[4];            // "fmt "
    uint32_t fmt_size;      // fmt chunk 大小 (16)
    uint16_t audio_format;  // 音频格式 (1 = PCM)
    uint16_t num_channels;  // 通道数
    uint32_t sample_rate;   // 采样率
    uint32_t byte_rate;     // 字节率
    uint16_t block_align;   // 块对齐
    uint16_t bits_per_sample; // 位深
    char data[4];           // "data"
    uint32_t data_size;     // 数据大小
} wav_header_t;

// 默认配置参数
#define DEFAULT_CARD         2
#define DEFAULT_DEVICE       0
#define DEFAULT_CHANNELS     2
#define DEFAULT_SAMPLE_RATE  48000
#define DEFAULT_FORMAT       PCM_FORMAT_S32_LE
#define DEFAULT_PERIOD_SIZE  192  // 增加到2048帧，减少断帧风险
#define DEFAULT_PERIOD_COUNT 4     // 增加到8个period，提供更大缓冲区
#define DEFAULT_OUTPUT_DIR   "/tmp"
#define SAMPLE_SIZE         4  // S32_LE每样本4字节

// 全局变量
static volatile int running = 1;
static int debug_mode = 0;

// 程序配置
typedef struct {
    int card;
    int device;
    int channels;
    int sample_rate;
    int format;
    int bits_per_sample;
    unsigned int period_size;
    unsigned int period_count;
    char output_file[256];
    int duration;  // 录音时长（秒），0表示无限制
} record_config_t;

// 信号处理函数
void signal_handler(int sig) {
    printf("\n接收到信号 %d，正在停止录音...\n", sig);
    running = 0;
}

// 写入WAV文件头
int write_wav_header(FILE *fp, record_config_t *config) {
    wav_header_t header;

    // 清零结构体
    memset(&header, 0, sizeof(header));

    // 填充WAV头信息
    memcpy(header.riff, "RIFF", 4);
    header.chunk_size = 0;  // 先填0，录音结束后更新
    memcpy(header.wave, "WAVE", 4);
    memcpy(header.fmt, "fmt ", 4);
    header.fmt_size = 16;
    header.audio_format = 1;  // PCM
    header.num_channels = config->channels;
    header.sample_rate = config->sample_rate;
    header.bits_per_sample = config->bits_per_sample;
    header.block_align = (header.bits_per_sample * header.num_channels) / 8;
    header.byte_rate = header.sample_rate * header.block_align;
    memcpy(header.data, "data", 4);
    header.data_size = 0;  // 先填0，录音结束后更新

    if (debug_mode) {
        printf("WAV头信息:\n");
        printf("  采样率: %u Hz\n", header.sample_rate);
        printf("  通道数: %u\n", header.num_channels);
        printf("  位深: %u bits\n", header.bits_per_sample);
        printf("  块对齐: %u bytes\n", header.block_align);
        printf("  字节率: %u bytes/sec\n", header.byte_rate);
    }

    // 写入文件头
    if (fwrite(&header, sizeof(header), 1, fp) != 1) {
        fprintf(stderr, "写入WAV文件头失败: %s\n", strerror(errno));
        return -1;
    }

    return 0;
}

// 更新WAV文件头（录音结束后调用）
int update_wav_header(FILE *fp, uint32_t data_size) {
    // WAV文件格式：
    // chunk_size = 整个文件大小 - 8 (不包括RIFF和chunk_size本身)
    // chunk_size = sizeof(wav_header_t) - 8 + data_size
    uint32_t chunk_size = data_size + sizeof(wav_header_t) - 8;

    if (debug_mode) {
        printf("更新WAV文件头:\n");
        printf("  数据大小: %u bytes\n", data_size);
        printf("  文件总大小: %u bytes\n", chunk_size + 8);
        printf("  预计时长: %.2f 秒\n",
               (float)data_size / (2 * 4 * 48000));  // 假设2通道，4字节/样本，48kHz
    }

    // 更新chunk_size (位置4)
    if (fseek(fp, 4, SEEK_SET) != 0) {
        fprintf(stderr, "文件定位失败 (chunk_size)\n");
        return -1;
    }
    if (fwrite(&chunk_size, sizeof(chunk_size), 1, fp) != 1) {
        fprintf(stderr, "更新chunk_size失败\n");
        return -1;
    }

    // 更新data_size (位置 sizeof(wav_header_t) - 4)
    if (fseek(fp, sizeof(wav_header_t) - 4, SEEK_SET) != 0) {
        fprintf(stderr, "文件定位失败 (data_size)\n");
        return -1;
    }
    if (fwrite(&data_size, sizeof(data_size), 1, fp) != 1) {
        fprintf(stderr, "更新data_size失败\n");
        return -1;
    }

    // 确保数据写入磁盘
    if (fflush(fp) != 0) {
        fprintf(stderr, "刷新文件缓冲区失败\n");
        return -1;
    }

    return 0;
}

// 验证WAV文件头信息
int verify_wav_file(const char *filename) {
    FILE *fp = fopen(filename, "rb");
    if (!fp) {
        fprintf(stderr, "无法打开文件进行验证: %s\n", strerror(errno));
        return -1;
    }

    wav_header_t header;
    if (fread(&header, sizeof(header), 1, fp) != 1) {
        fprintf(stderr, "读取WAV文件头失败\n");
        fclose(fp);
        return -1;
    }

    // 获取文件实际大小
    fseek(fp, 0, SEEK_END);
    long file_size = ftell(fp);
    fclose(fp);

    // 计算时长
    float duration = (float)header.data_size / header.byte_rate;
    float file_duration = (float)(file_size - sizeof(wav_header_t)) / header.byte_rate;

    printf("\nWAV文件验证结果:\n");
    printf("  文件: %s\n", filename);
    printf("  文件大小: %ld bytes\n", file_size);
    printf("  头部信息:\n");
    printf("    采样率: %u Hz\n", header.sample_rate);
    printf("    通道数: %u\n", header.num_channels);
    printf("    位深: %u bits\n", header.bits_per_sample);
    printf("    字节率: %u bytes/sec\n", header.byte_rate);
    printf("    数据大小: %u bytes\n", header.data_size);
    printf("    块大小: %u bytes\n", header.chunk_size);
    printf("  计算时长:\n");
    printf("    根据头部: %.2f秒\n", duration);
    printf("    根据文件: %.2f秒\n", file_duration);

    if (fabs(duration - file_duration) > 0.1) {
        printf("  警告: 时长计算不一致，可能存在问题!\n");
        return -1;
    } else {
        printf("  验证通过: 时长计算一致\n");
    }

    return 0;
}

// 解析设备名称 (格式: hw:card,device)
int parse_device_name(const char *device_name, int *card, int *device) {
    if (sscanf(device_name, "hw:%d,%d", card, device) == 2) {
        return 0;
    }
    return -1;
}

// 检查设备可用性
int check_device_availability(int card, int device, int channels, int sample_rate) {
    struct pcm_config config = {
        .channels = channels,
        .rate = sample_rate,
        .period_size = DEFAULT_PERIOD_SIZE,
        .period_count = DEFAULT_PERIOD_COUNT,
        .format = DEFAULT_FORMAT,
        .start_threshold = 0,
        .stop_threshold = 0,
        .silence_threshold = 0,
    };

    struct pcm *pcm = pcm_open(card, device, PCM_IN, &config);
    if (!pcm || !pcm_is_ready(pcm)) {
        if (pcm) {
            if (debug_mode) {
                fprintf(stderr, "设备hw:%d,%d不可用: %s\n", 
                        card, device, pcm_get_error(pcm));
            }
            pcm_close(pcm);
        }
        return -1;
    }

    pcm_close(pcm);
    return 0;
}

// 列出可用的音频设备
void list_audio_devices() {
    printf("\n=== 可用的录音设备 (tinyalsa) ===\n");
    
    for (int card = 0; card <= 3; card++) {
        for (int device = 0; device <= 1; device++) {
            if (check_device_availability(card, device, DEFAULT_CHANNELS, DEFAULT_SAMPLE_RATE) == 0) {
                printf("  ✓ hw:%d,%d (可用)\n", card, device);
            } else {
                if (debug_mode) {
                    printf("  ✗ hw:%d,%d (不可用)\n", card, device);
                }
            }
        }
    }
    printf("\n");
}

// 生成输出文件名
void generate_filename(record_config_t *config) {
    time_t now;
    struct tm *tm_info;
    char timestamp[32];
    
    time(&now);
    tm_info = localtime(&now);
    strftime(timestamp, sizeof(timestamp), "%Y%m%d_%H%M%S", tm_info);
    
    snprintf(config->output_file, sizeof(config->output_file),
             "%s/record_%s_ch%d_%dHz.wav", 
             DEFAULT_OUTPUT_DIR, timestamp, config->channels, config->sample_rate);
}

// 音频录音主函数
int record_audio(record_config_t *config) {
    struct pcm_config pcm_config = {
        .channels = config->channels,
        .rate = config->sample_rate,
        .period_size = config->period_size,
        .period_count = config->period_count,
        .format = config->format,
        .start_threshold = config->period_size,  // 设置合适的启动阈值
        .stop_threshold = config->period_size * config->period_count,
        .silence_threshold = 0,
    };

    // 打开PCM设备
    struct pcm *pcm = pcm_open(config->card, config->device, PCM_IN, &pcm_config);
    if (!pcm || !pcm_is_ready(pcm)) {
        fprintf(stderr, "无法打开录音设备hw:%d,%d: %s\n", 
                config->card, config->device, 
                pcm ? pcm_get_error(pcm) : "设备创建失败");
        if (pcm) {
            pcm_close(pcm);
        }
        return -1;
    }

    // 获取实际配置
    unsigned int actual_rate = pcm_get_rate(pcm);
    unsigned int actual_channels = pcm_get_channels(pcm);
    unsigned int buffer_size = pcm_get_buffer_size(pcm);

    // 更新配置以匹配实际硬件参数
    if (actual_rate != (unsigned int)config->sample_rate) {
        printf("警告: 实际采样率 %u Hz 与请求的 %d Hz 不匹配\n",
               actual_rate, config->sample_rate);
        config->sample_rate = actual_rate;
    }
    if (actual_channels != (unsigned int)config->channels) {
        printf("警告: 实际通道数 %u 与请求的 %d 不匹配\n",
               actual_channels, config->channels);
        config->channels = actual_channels;
    }

    printf("PCM设备已打开: hw:%d,%d\n", config->card, config->device);
    printf("实际配置: %u通道, %uHz, 缓冲区大小: %u帧\n",
           actual_channels, actual_rate, buffer_size);

    // 打开输出文件
    FILE *fp = fopen(config->output_file, "wb");
    if (!fp) {
        fprintf(stderr, "无法创建输出文件: %s\n", strerror(errno));
        pcm_close(pcm);
        return -1;
    }

    // 写入WAV文件头
    if (write_wav_header(fp, config) < 0) {
        fclose(fp);
        pcm_close(pcm);
        return -1;
    }

    // 分配录音缓冲区
    size_t buffer_bytes = config->period_size * config->channels * SAMPLE_SIZE;
    void *buffer = malloc(buffer_bytes);
    if (!buffer) {
        fprintf(stderr, "无法分配录音缓冲区\n");
        fclose(fp);
        pcm_close(pcm);
        return -1;
    }

    printf("开始录音到文件: %s\n", config->output_file);
    printf("采集参数: %d通道, %dHz, %d位深\n", 
           config->channels, config->sample_rate, config->bits_per_sample);
    
    if (config->duration > 0) {
        printf("录音时长: %d秒\n", config->duration);
    } else {
        printf("持续录音，按Ctrl+C停止\n");
    }

    // 录音循环
    uint32_t total_bytes = 0;
    time_t start_time = time(NULL);
    unsigned int frames_processed = 0;
    int consecutive_errors = 0;
    unsigned int buffer_overruns = 0;

    // 检查PCM设备状态
    if (!pcm_is_ready(pcm)) {
        fprintf(stderr, "PCM设备未就绪，无法启动录音: %s\n", pcm_get_error(pcm));
        free(buffer);
        fclose(fp);
        pcm_close(pcm);
        return -1;
    }
    else {
        if (buffer) {
            int ret = pcm_readi(pcm, buffer, pcm_config.period_size);
            if (ret > 0) {
                printf("采集设备通过读取启动成功\n");
            }
        }
    }

    /*
    // 启动PCM设备
    if (pcm_start(pcm) < 0) {
        fprintf(stderr, "启动PCM设备失败: %s\n", pcm_get_error(pcm));
        fprintf(stderr, "设备信息: hw:%d,%d, 采样率:%d, 通道数:%d\n",
                config->card, config->device, config->sample_rate, config->channels);
        // 启动失败，需要退出录音
        free(buffer);
        fclose(fp);
        pcm_close(pcm);
        return -1;
    }
    */

    while (running) {
        // 检查是否达到指定时长
        if (config->duration > 0) {
            time_t current_time = time(NULL);
            if (current_time - start_time >= config->duration) {
                printf("达到指定录音时长，停止录音\n");
                break;
            }
        }

        // 从PCM设备读取音频数据
        int frames_read = pcm_readi(pcm, buffer, config->period_size);

        if (frames_read < 0) {
            const char *error_str = pcm_get_error(pcm);
            fprintf(stderr, "读取音频数据失败: %s\n", error_str);
            consecutive_errors++;

            // 改进的错误恢复机制
            if (consecutive_errors < 10) {
                printf("尝试恢复PCM设备... (错误次数: %d)\n", consecutive_errors);

                // 停止设备
                pcm_stop(pcm);
                usleep(5000);  // 等待5ms

                // 重新准备设备
                if (pcm_prepare(pcm) < 0) {
                    fprintf(stderr, "PCM prepare失败: %s\n", pcm_get_error(pcm));
                }

                // 重新启动设备
                if (pcm_start(pcm) < 0) {
                    fprintf(stderr, "PCM start失败: %s\n", pcm_get_error(pcm));
                }

                usleep(10000);  // 等待10ms让设备稳定
                continue;
            } else {
                fprintf(stderr, "连续错误过多，停止录音\n");
                break;
            }
        }

        if (frames_read == 0) {
            if (debug_mode) {
                printf("读取到0帧数据，继续...\n");
            }
            usleep(1000);
            continue;
        }

        consecutive_errors = 0;  // 重置错误计数
        frames_processed += frames_read;

        // 检查是否有缓冲区溢出
        if (frames_read < (int)config->period_size) {
            buffer_overruns++;
            if (debug_mode) {
                printf("警告: 只读取到 %d 帧，期望 %u 帧 (溢出次数: %u)\n",
                       frames_read, config->period_size, buffer_overruns);
            }
        }

        // 计算实际读取的字节数
        size_t bytes_read = frames_read * config->channels * SAMPLE_SIZE;
        
        // 写入WAV文件
        if (fwrite(buffer, 1, bytes_read, fp) != bytes_read) {
            fprintf(stderr, "写入文件失败: %s\n", strerror(errno));
            break;
        }

        total_bytes += bytes_read;

        // 显示进度信息
        if (debug_mode && frames_processed % (config->sample_rate / 4) == 0) {
            float seconds = (float)frames_processed / config->sample_rate;
            printf("已录音 %.1f秒, 已写入 %u 字节, 缓冲区溢出: %u 次\n",
                   seconds, total_bytes, buffer_overruns);
        } else if (frames_processed % config->sample_rate == 0) {
            // 每秒显示一次进度
            float seconds = (float)frames_processed / config->sample_rate;
            if (buffer_overruns > 0) {
                printf("录音进度: %.1f秒 (溢出: %u)\r", seconds, buffer_overruns);
            } else {
                printf("录音进度: %.1f秒\r", seconds);
            }
            fflush(stdout);
        }
    }

    printf("\n录音完成\n");

    // 计算实际录音时长
    float actual_duration = (float)frames_processed / config->sample_rate;
    float expected_bytes = frames_processed * config->channels * (config->bits_per_sample / 8);

    printf("录音统计信息:\n");
    printf("  实际时长: %.2f秒\n", actual_duration);
    printf("  处理帧数: %u帧\n", frames_processed);
    printf("  实际字节: %u bytes\n", total_bytes);
    printf("  期望字节: %.0f bytes\n", expected_bytes);

    if (fabs(total_bytes - expected_bytes) > 1.0) {
        printf("  警告: 实际字节数与期望不匹配，差异: %.0f bytes\n",
               fabs(total_bytes - expected_bytes));
    }

    // 显示质量统计信息
    if (buffer_overruns > 0) {
        printf("录音质量: 警告 - 检测到 %u 次缓冲区溢出\n", buffer_overruns);
        printf("建议: 增加period_size或period_count，或降低系统负载\n");
    } else {
        printf("录音质量: 良好 (无缓冲区溢出)\n");
    }

    // 更新WAV文件头
    if (update_wav_header(fp, total_bytes) < 0) {
        fprintf(stderr, "警告: 更新WAV文件头失败\n");
    } else {
        printf("WAV文件头已更新，时长应为: %.2f秒\n", actual_duration);
    }

    // 清理资源
    free(buffer);
    fclose(fp);
    pcm_close(pcm);

    printf("录音文件已保存: %s\n", config->output_file);

    // 验证WAV文件
    if (verify_wav_file(config->output_file) == 0) {
        printf("WAV文件验证通过\n");
    } else {
        printf("WAV文件验证失败，可能存在时间显示问题\n");
    }

    return 0;
}

// 显示使用帮助
void show_usage(const char *prog_name) {
    printf("用法: %s [选项]\n", prog_name);
    printf("嵌入式Linux音频录音测试程序 (tinyalsa 2.0.0)\n\n");
    printf("选项:\n");
    printf("  -d, --device <hw:card,device>  指定录音设备 (默认: hw:%d,%d)\n", 
           DEFAULT_CARD, DEFAULT_DEVICE);
    printf("  -c, --channels <n>             指定通道数 (默认: %d)\n", DEFAULT_CHANNELS);
    printf("  -r, --rate <hz>                指定采样率 (默认: %d)\n", DEFAULT_SAMPLE_RATE);
    printf("  -f, --file <path>              指定输出文件路径\n");
    printf("  -t, --time <seconds>           指定录音时长(秒)，0表示无限制 (默认: 0)\n");
    printf("  -o, --output-dir <path>        指定输出目录 (默认: %s)\n", DEFAULT_OUTPUT_DIR);
    printf("  -p, --period-size <frames>     指定period大小 (默认: %d)\n", DEFAULT_PERIOD_SIZE);
    printf("  -b, --buffer-periods <count>   指定缓冲区period数量 (默认: %d)\n", DEFAULT_PERIOD_COUNT);
    printf("  -D, --debug                    启用调试模式\n");
    printf("  -l, --list                     列出可用的录音设备\n");
    printf("  -h, --help                     显示此帮助信息\n");
    printf("\n");
    printf("示例:\n");
    printf("  %s                             使用默认配置录音\n", prog_name);
    printf("  %s -l                          列出可用设备\n", prog_name);
    printf("  %s -d hw:2,0 -c 2 -r 48000     从hw:2,0录制2通道48kHz音频\n", prog_name);
    printf("  %s -t 30 -f /tmp/test.wav      录制30秒保存到指定文件\n", prog_name);
    printf("  %s -D                          启用调试模式\n", prog_name);
    printf("\n");
    printf("注意:\n");
    printf("  - 默认格式: S32_LE (32位有符号小端)\n");
    printf("  - 如果不指定输出文件，将在 %s 目录下生成带时间戳的文件名\n", DEFAULT_OUTPUT_DIR);
    printf("  - 按Ctrl+C可随时停止录音\n");
    printf("  - 程序需要访问音频设备权限，可能需要root权限或加入audio组\n");
}

// 主函数
int main(int argc, char *argv[]) {
    record_config_t config = {
        .card = DEFAULT_CARD,
        .device = DEFAULT_DEVICE,
        .channels = DEFAULT_CHANNELS,
        .sample_rate = DEFAULT_SAMPLE_RATE,
        .format = DEFAULT_FORMAT,
        .bits_per_sample = 32,  // S32_LE = 32位
        .period_size = DEFAULT_PERIOD_SIZE,
        .period_count = DEFAULT_PERIOD_COUNT,
        .duration = 0,  // 0表示无限制
        .output_file = {0}
    };

    int list_devices = 0;
    int custom_output = 0;
    char output_dir[256] = DEFAULT_OUTPUT_DIR;
    
    struct option long_options[] = {
        {"device", required_argument, 0, 'd'},
        {"channels", required_argument, 0, 'c'},
        {"rate", required_argument, 0, 'r'},
        {"file", required_argument, 0, 'f'},
        {"time", required_argument, 0, 't'},
        {"output-dir", required_argument, 0, 'o'},
        {"period-size", required_argument, 0, 'p'},
        {"buffer-periods", required_argument, 0, 'b'},
        {"debug", no_argument, 0, 'D'},
        {"list", no_argument, 0, 'l'},
        {"help", no_argument, 0, 'h'},
        {0, 0, 0, 0}
    };

    int opt;
    while ((opt = getopt_long(argc, argv, "d:c:r:f:t:o:p:b:Dlh", long_options, NULL)) != -1) {
        switch (opt) {
            case 'd':
                if (parse_device_name(optarg, &config.card, &config.device) < 0) {
                    fprintf(stderr, "错误: 无效的设备名称格式，应为 hw:card,device\n");
                    return 1;
                }
                break;
            case 'c':
                config.channels = atoi(optarg);
                if (config.channels <= 0 || config.channels > 64) {
                    fprintf(stderr, "错误: 通道数必须在1-64之间\n");
                    return 1;
                }
                break;
            case 'r':
                config.sample_rate = atoi(optarg);
                if (config.sample_rate < 8000 || config.sample_rate > 192000) {
                    fprintf(stderr, "错误: 采样率必须在8000-192000Hz之间\n");
                    return 1;
                }
                break;
            case 'f':
                strncpy(config.output_file, optarg, sizeof(config.output_file) - 1);
                config.output_file[sizeof(config.output_file) - 1] = '\0';
                custom_output = 1;
                break;
            case 't':
                config.duration = atoi(optarg);
                if (config.duration < 0) {
                    fprintf(stderr, "错误: 录音时长不能为负数\n");
                    return 1;
                }
                break;
            case 'o':
                strncpy(output_dir, optarg, sizeof(output_dir) - 1);
                output_dir[sizeof(output_dir) - 1] = '\0';
                break;
            case 'p':
                config.period_size = atoi(optarg);
                if (config.period_size < 64 || config.period_size > 8192) {
                    fprintf(stderr, "错误: period_size必须在64-8192之间\n");
                    return 1;
                }
                break;
            case 'b':
                config.period_count = atoi(optarg);
                if (config.period_count < 2 || config.period_count > 32) {
                    fprintf(stderr, "错误: period_count必须在2-32之间\n");
                    return 1;
                }
                break;
            case 'D':
                debug_mode = 1;
                break;
            case 'l':
                list_devices = 1;
                break;
            case 'h':
                show_usage(argv[0]);
                return 0;
            default:
                show_usage(argv[0]);
                return 1;
        }
    }

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    printf("=== 嵌入式Linux音频录音测试程序 (tinyalsa) ===\n");

    // 如果只是列出设备，执行后退出
    if (list_devices) {
        list_audio_devices();
        return 0;
    }

    // 检查设备可用性
    if (check_device_availability(config.card, config.device, 
                                config.channels, config.sample_rate) < 0) {
        fprintf(stderr, "错误: 设备hw:%d,%d不可用\n", config.card, config.device);
        printf("提示: 运行 '%s -l' 查看可用设备\n", argv[0]);
        return 1;
    }

    // 生成输出文件名（如果未指定）
    if (!custom_output) {
        generate_filename(&config);
        // 如果指定了输出目录，更新路径
        if (strcmp(output_dir, DEFAULT_OUTPUT_DIR) != 0) {
            char temp_filename[256];
            char *basename = strrchr(config.output_file, '/');
            if (basename) {
                basename++; // 跳过斜杠
                snprintf(temp_filename, sizeof(temp_filename), "%s/%s", output_dir, basename);
                strncpy(config.output_file, temp_filename, sizeof(config.output_file) - 1);
                config.output_file[sizeof(config.output_file) - 1] = '\0';
            }
        }
    }

    // 检查输出目录是否存在
    char dir_path[256];
    strncpy(dir_path, config.output_file, sizeof(dir_path) - 1);
    dir_path[sizeof(dir_path) - 1] = '\0';
    char *last_slash = strrchr(dir_path, '/');
    if (last_slash) {
        *last_slash = '\0';
        if (access(dir_path, W_OK) != 0) {
            fprintf(stderr, "警告: 输出目录 %s 不可写，尝试创建...\n", dir_path);
            // 这里可以尝试创建目录，但为了简单，我们只是警告
        }
    }

    // 显示配置信息
    printf("录音配置:\n");
    printf("  设备: hw:%d,%d\n", config.card, config.device);
    printf("  通道数: %d\n", config.channels);
    printf("  采样率: %dHz\n", config.sample_rate);
    printf("  格式: S32_LE (32位有符号小端)\n");
    printf("  Period大小: %u帧\n", config.period_size);
    printf("  缓冲区: %u个period\n", config.period_count);
    printf("  输出文件: %s\n", config.output_file);
    if (config.duration > 0) {
        printf("  录音时长: %d秒\n", config.duration);
    } else {
        printf("  录音时长: 无限制 (按Ctrl+C停止)\n");
    }
    printf("\n");

    // 开始录音
    int result = record_audio(&config);
    
    if (result == 0) {
        printf("录音成功完成!\n");
    } else {
        printf("录音失败\n");
        return 1;
    }

    return 0;
}