#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <signal.h>
#include <getopt.h>
#include <pthread.h>
#include <sched.h>
#include <tinyalsa/asoundlib.h>

// 必要的定义（如果系统头文件不完整）
#ifndef PCM_FORMAT_S16_LE
#define PCM_FORMAT_S16_LE 0
#endif

#ifndef PCM_FORMAT_S32_LE
#define PCM_FORMAT_S32_LE 1
#endif

#ifndef PCM_IN
#define PCM_IN  0x10000000
#endif

#ifndef PCM_OUT
#define PCM_OUT 0x00000000
#endif

// 音频参数定义
#define SAMPLE_RATE     48000
#define DEFAULT_CAPTURE_CHANNELS 32
#define DEFAULT_PLAYBACK_CHANNELS 2
#define FORMAT          PCM_FORMAT_S32_LE
#define SAMPLE_SIZE     4  // S32_LE每样本4字节

// 音频设备状态枚举
typedef enum {
    DEVICE_STATE_CLOSED = 0,
    DEVICE_STATE_OPENED,
    DEVICE_STATE_PREPARED,
    DEVICE_STATE_RUNNING,
    DEVICE_STATE_ERROR
} device_state_t;

// 音频设备结构体
typedef struct {
    struct pcm *handle;
    char device_name[64];
    int channels;
    unsigned int period_size;
    unsigned int buffer_size;
    device_state_t state;
    int underrun_count;
    int card;      // tinyalsa需要的card号
    int device;    // tinyalsa需要的device号
} audio_device_t;

// 全局变量
static int running = 1;
static audio_device_t capture_dev = {0};
static audio_device_t playback_dev = {0};
static int ch1 = 0, ch2 = 1;  // 默认选择采集通道0和1
static int pch1 = 0, pch2 = 1; // 默认输出到播放通道0和1
static int frame_time = 4;    // 默认4ms帧长
static int debug_mode = 0;    // 调试模式
static char capture_device[64] = "hw:0,0";  // 默认采集设备
static char playback_device[64] = "hw:2,0"; // 默认播放设备
static int capture_channels = DEFAULT_CAPTURE_CHANNELS;  // 采集通道数
static int playback_channels = DEFAULT_PLAYBACK_CHANNELS; // 播放通道数

// 信号处理函数
void signal_handler(int sig) {
    printf("\n接收到信号 %d, 正在退出...\n", sig);
    running = 0;
}

// 解析设备名称，提取card和device号
int parse_device_name(const char *device_name, int *card, int *device) {
    if (sscanf(device_name, "hw:%d,%d", card, device) == 2) {
        return 0;
    }
    return -1;
}

// 检查设备是否可用
int check_device_availability(const char *device_name, int is_capture, int channels) {
    int card, device;
    if (parse_device_name(device_name, &card, &device) < 0) {
        return -1;
    }

    // 使用更保守的配置进行测试
    struct pcm_config config = {
        .channels = channels,
        .rate = SAMPLE_RATE,
        .period_size = 1024,  // 使用较小的period_size进行测试
        .period_count = 4,
        .format = PCM_FORMAT_S32_LE,
        .start_threshold = 0,
        .stop_threshold = 0,
        .silence_threshold = 0,
    };

    unsigned int flags = is_capture ? PCM_IN : PCM_OUT;
    struct pcm *pcm = pcm_open(card, device, flags, &config);

    if (!pcm || !pcm_is_ready(pcm)) {
        if (pcm) {
            pcm_close(pcm);
        }
        return -1;
    }

    pcm_close(pcm);
    return 0;
}

// 列出可用的音频设备
void list_audio_devices() {
    printf("\n=== 音频设备检查 (tinyalsa) ===\n");

    // 检查常见的设备
    const char *test_devices[] = {
        "hw:0,0", "hw:1,0", "hw:2,0", "hw:3,0", NULL
    };

    printf("检查播放设备:\n");
    for (int i = 0; test_devices[i] != NULL; i++) {
        if (check_device_availability(test_devices[i], 0, playback_channels) == 0) {
            printf("  ✓ %s (可用)\n", test_devices[i]);
        } else {
            printf("  ✗ %s (不可用)\n", test_devices[i]);
        }
    }

    printf("检查录音设备:\n");
    for (int i = 0; test_devices[i] != NULL; i++) {
        if (check_device_availability(test_devices[i], 1, capture_channels) == 0) {
            printf("  ✓ %s (可用)\n", test_devices[i]);
        } else {
            printf("  ✗ %s (不可用)\n", test_devices[i]);
        }
    }
    printf("\n");
}

// 解析通道参数 (格式: "ch1:ch2")
int parse_channel_pair(const char *str, int *ch1, int *ch2) {
    char *endptr;
    char *colon_pos = strchr(str, ':');

    if (!colon_pos) {
        fprintf(stderr, "错误: 通道格式应为 'ch1:ch2'，例如 '0:1'\n");
        return -1;
    }

    // 解析第一个通道
    *ch1 = strtol(str, &endptr, 10);
    if (endptr != colon_pos) {
        fprintf(stderr, "错误: 无效的第一个通道号\n");
        return -1;
    }

    // 解析第二个通道
    *ch2 = strtol(colon_pos + 1, &endptr, 10);
    if (*endptr != '\0') {
        fprintf(stderr, "错误: 无效的第二个通道号\n");
        return -1;
    }

    return 0;
}

// 设置进程优先级
int set_high_priority() {
    struct sched_param param;
    param.sched_priority = 50;  // tinyalsa使用较低的实时优先级

    if (sched_setscheduler(0, SCHED_FIFO, &param) != 0) {
        perror("设置实时优先级失败");
        return -1;
    }

    printf("进程优先级已设置为实时模式，优先级: %d\n", param.sched_priority);
    return 0;
}

#ifndef UINT_MAX
#define UINT_MAX    (~0U)
#endif

// 初始化音频设备 - 修复版本
int init_audio_device(audio_device_t *dev, const char *device_name,
                     int is_capture, int channels) {
    // 初始化设备结构体
    memset(dev, 0, sizeof(audio_device_t));
    strncpy(dev->device_name, device_name, sizeof(dev->device_name) - 1);
    dev->device_name[sizeof(dev->device_name) - 1] = '\0';
    dev->channels = channels;
    dev->state = DEVICE_STATE_CLOSED;
    dev->underrun_count = 0;
    
    // 解析设备名称
    if (parse_device_name(device_name, &dev->card, &dev->device) < 0) {
        fprintf(stderr, "无法解析设备名称: %s\n", device_name);
        dev->state = DEVICE_STATE_ERROR;
        return -1;
    }
    
    // 计算合适的period_size
    unsigned int period_size = (SAMPLE_RATE * frame_time) / 1000;
    
    // 确保period_size是2的幂次方或者是合理的大小
    if (period_size < 64) period_size = 64;
    if (period_size > 4096) period_size = 4096;
    
    // 对于某些驱动，period_size需要是特定的倍数
    period_size = (period_size / 32) * 32;  // 32的倍数
    
    dev->period_size = period_size;
    dev->buffer_size = period_size * 4;  // 4个period的缓冲区

    // 改进的PCM配置
    struct pcm_config config = {
        .channels = channels,
        .rate = SAMPLE_RATE,
        .period_size = dev->period_size,
        .period_count = 4,
        .format = PCM_FORMAT_S32_LE,
        .start_threshold = dev->period_size,
        .stop_threshold = UINT_MAX,
        .silence_threshold = 0,
    };
    
    // 打开设备前添加延迟
    usleep(10000);
    
    unsigned int flags = is_capture ? PCM_IN : PCM_OUT;
    dev->handle = pcm_open(dev->card, dev->device, flags, &config);
    
    if (!dev->handle || !pcm_is_ready(dev->handle)) {
        // 详细的错误信息
        const char *error = dev->handle ? pcm_get_error(dev->handle) : "设备创建失败";
        fprintf(stderr, "PCM设备打开失败: %s, 错误: %s\n", device_name, error);
        
        if (dev->handle) {
            pcm_close(dev->handle);
            dev->handle = NULL;
        }
        dev->state = DEVICE_STATE_ERROR;
        return -1;
    }
    
    // 验证实际配置
    unsigned int actual_rate = pcm_get_rate(dev->handle);
    unsigned int actual_channels = pcm_get_channels(dev->handle);
    unsigned int actual_format = pcm_get_format(dev->handle);
    
    if (actual_format != PCM_FORMAT_S32_LE) {
        fprintf(stderr, "警告: 实际格式 %u 与请求的格式不符\n", actual_format);
    }
    
    if (actual_rate != SAMPLE_RATE) {
        fprintf(stderr, "警告: 实际采样率 %u Hz 与请求的 %d Hz 不符\n", 
                actual_rate, SAMPLE_RATE);
    }
    
    if (actual_channels != (unsigned int)channels) {
        fprintf(stderr, "警告: 实际通道数 %u 与请求的 %d 不符\n", 
                actual_channels, channels);
    }

    // 获取实际参数
    dev->buffer_size = pcm_get_buffer_size(dev->handle);
    dev->state = DEVICE_STATE_PREPARED;
    
    printf("设备初始化成功(tinyalsa): %s, %d通道, %dHz, 缓冲区大小:%u\n",
           device_name, channels, SAMPLE_RATE, dev->buffer_size);
    
    if (debug_mode) {
        printf("  实际配置: rate=%u, channels=%u, buffer_size=%u\n",
               actual_rate, actual_channels, dev->buffer_size);
    }
    
    return 0;
}

// 初始化采集设备的包装函数
int init_capture_device() {
    return init_audio_device(&capture_dev, capture_device, 1, capture_channels);
}

// 初始化播放设备的包装函数
int init_playback_device() {
    return init_audio_device(&playback_dev, playback_device, 0, playback_channels);
}

// sync_devices函数负责正确的启动顺序
int sync_devices() {
    printf("准备启动设备...\n");
    
    // 1. 预填充播放设备缓冲区
    size_t silence_size = playback_dev.period_size * playback_dev.channels * SAMPLE_SIZE;
    void *silence_buffer = calloc(1, silence_size);
    
    if (!silence_buffer) {
        fprintf(stderr, "无法分配静音缓冲区\n");
        return -1;
    }
    
    printf("预填充播放缓冲区...\n");
    // 预填充静音数据 - 这本身可能就启动了播放设备
    for (int i = 0; i < 2; i++) {
        int ret = pcm_writei(playback_dev.handle, silence_buffer, playback_dev.period_size);
        if (ret < 0) {
            fprintf(stderr, "预填充播放缓冲区失败: %s\n", pcm_get_error(playback_dev.handle));
            free(silence_buffer);
            return -1;
        }
    }
    free(silence_buffer);
    
    // 2. 检查播放设备是否已经启动
    if (pcm_is_ready(playback_dev.handle)) {
        // 很多tinyalsa实现在第一次写入数据时自动启动
        printf("播放设备已自动启动\n");
        playback_dev.state = DEVICE_STATE_RUNNING;
    } else {
        // 如果还没启动，尝试手动启动
        if (pcm_start(playback_dev.handle) < 0) {
            fprintf(stderr, "启动播放设备失败: %s\n", pcm_get_error(playback_dev.handle));
            return -1;
        }
        playback_dev.state = DEVICE_STATE_RUNNING;
        printf("播放设备已启动\n");
    }
    
    // 3. 对于采集设备，很多tinyalsa实现不需要显式启动
    if (pcm_is_ready(capture_dev.handle)) {
        printf("采集设备已准备就绪\n");
        capture_dev.state = DEVICE_STATE_RUNNING;
        
        // 某些驱动可能需要一次读取来启动
        void *dummy_buffer = malloc(capture_dev.period_size * capture_dev.channels * SAMPLE_SIZE);
        if (dummy_buffer) {
            int ret = pcm_readi(capture_dev.handle, dummy_buffer, capture_dev.period_size);
            if (ret > 0) {
                printf("采集设备通过读取启动成功\n");
            }
            free(dummy_buffer);
        }
    } else {
        fprintf(stderr, "采集设备未准备就绪\n");
        return -1;
    }
    
    printf("设备启动完成\n");
    return 0;
}

// 恢复设备 - 增强版本
int recover_device(audio_device_t *dev) {
    printf("设备 %s 缓冲区异常，恢复中(tinyalsa)...\n", dev->device_name);
    
    dev->underrun_count++;
    
    // 尝试恢复PCM流
    if (dev->handle && pcm_is_ready(dev->handle)) {
        // 对于tinyalsa，尝试重新准备设备
        int ret = pcm_prepare(dev->handle);
        if (ret != 0) {
            fprintf(stderr, "设备恢复失败: %s\n", pcm_get_error(dev->handle));
            return -1;
        }
    }
    
    return 0;
}

int handle_pcm_error(audio_device_t *dev, int err, const char *operation) {
    if (err >= 0) {
        return 0;
    }
    
    const char *error_str = dev->handle ? pcm_get_error(dev->handle) : "未知错误";
    
    // 更详细的错误分类处理
    switch (err) {
        case -EPIPE:  // Buffer underrun/overrun
            printf("缓冲区异常 (%s)，尝试恢复...\n", operation);
            return recover_device(dev);
            
        case -ESTRPIPE:  // Device suspended
            printf("设备被挂起，等待恢复...\n");
            usleep(500000);  // 等待500ms让设备稳定
            return recover_device(dev);
            
        case -EAGAIN:  // Device busy
            if (debug_mode) {
                printf("设备忙碌，稍后重试\n");
            }
            usleep(1000);
            return 0;
            
        case -EIO:  // I/O error
            fprintf(stderr, "I/O错误: %s\n", error_str);
            return -1;
            
        default:
            fprintf(stderr, "PCM错误 (%s): %s, code=%d\n", operation, error_str, err);
            return -1;
    }
}

// 音频处理主循环 - 修复版本
void audio_loop() {
    // 使用更小的帧大小以减少延迟和错误
    unsigned int frames_to_process = capture_dev.period_size;
    
    // 确保frames_to_process是合理的
    if (frames_to_process == 0) {
        frames_to_process = (SAMPLE_RATE * frame_time) / 1000;
        if (frames_to_process < 64) frames_to_process = 64;
        if (frames_to_process > 1024) frames_to_process = 1024;
    }
    
    unsigned int total_frames_processed = 0;

    // 分配缓冲区 - 添加安全边界
    size_t capture_buffer_size = frames_to_process * capture_dev.channels * SAMPLE_SIZE;
    size_t playback_buffer_size = frames_to_process * playback_dev.channels * SAMPLE_SIZE;
    
    int32_t *capture_buffer = malloc(capture_buffer_size + 64);  // 额外的安全边界
    int32_t *playback_buffer = malloc(playback_buffer_size + 64);

    if (!capture_buffer || !playback_buffer) {
        fprintf(stderr, "无法分配音频缓冲区\n");
        goto cleanup;
    }

    // 清零缓冲区
    memset(capture_buffer, 0, capture_buffer_size);
    memset(playback_buffer, 0, playback_buffer_size);

    printf("开始音频处理循环(tinyalsa)\n");
    printf("处理帧大小: %u, 缓冲区大小: %zu字节\n", frames_to_process, capture_buffer_size);
    printf("通道映射: 采集%d->播放%d, 采集%d->播放%d\n", ch1, pch1, ch2, pch2);
    printf("按 Ctrl+C 停止程序\n");

    int consecutive_errors = 0;
    
    while (running) {
        // 从采集设备读取数据
        int frames_read = pcm_readi(capture_dev.handle, capture_buffer, frames_to_process);

        if (frames_read < 0) {
            if (handle_pcm_error(&capture_dev, frames_read, "读取") == 0) {
                consecutive_errors = 0;  // 错误已恢复
                continue;
            } else {
                consecutive_errors++;
                if (consecutive_errors > 10) {
                    fprintf(stderr, "连续错误过多，退出\n");
                    break;
                }
                usleep(10000);  // 等待10ms后重试
                continue;
            }
        }
        
        if (frames_read == 0) {
            if (debug_mode) {
                printf("读取到0帧，继续...\n");
            }
            usleep(1000);
            continue;
        }

        consecutive_errors = 0;  // 重置错误计数器
        total_frames_processed += frames_read;

        // 提取选定的通道数据
        int32_t max_sample = 0;

        // 首先将所有播放通道填充0
        memset(playback_buffer, 0, playback_buffer_size);

        // 然后填充指定的播放通道
        for (unsigned int i = 0; i < (unsigned int)frames_read; i++) {
            // 处理第一个通道映射: capture ch1 -> playback pch1
            if (ch1 < capture_dev.channels && pch1 < playback_dev.channels) {
                int32_t sample = capture_buffer[i * capture_dev.channels + ch1];
                playback_buffer[i * playback_dev.channels + pch1] = sample;

                if (abs(sample) > abs(max_sample)) {
                    max_sample = sample;
                }
            }

            // 处理第二个通道映射: capture ch2 -> playback pch2
            if (ch2 < capture_dev.channels && pch2 < playback_dev.channels) {
                int32_t sample = capture_buffer[i * capture_dev.channels + ch2];
                playback_buffer[i * playback_dev.channels + pch2] = sample;

                if (abs(sample) > abs(max_sample)) {
                    max_sample = sample;
                }
            }
        }

        // 调试信息：显示信号强度
        if (debug_mode && total_frames_processed % (SAMPLE_RATE/4) == 0) {
            printf("处理 %u 帧, 最大采样值: %d (0x%08X), 总帧数: %u\n", 
                   frames_read, max_sample, max_sample, total_frames_processed);
        }

        // 向播放设备写入数据
        int frames_written = pcm_writei(playback_dev.handle, playback_buffer, frames_read);

        if (frames_written < 0) {
            if (handle_pcm_error(&playback_dev, frames_written, "写入") != 0) {
                consecutive_errors++;
                if (consecutive_errors > 10) {
                    fprintf(stderr, "播放设备错误过多，退出\n");
                    break;
                }
            }
            continue;
        }
        
        // 简单的流量控制
        if (debug_mode && total_frames_processed % SAMPLE_RATE == 0) {
            printf("运行中... 已处理 %u 帧 (%.1f秒)\n", 
                   total_frames_processed, (float)total_frames_processed / SAMPLE_RATE);
        }
    }

cleanup:
    if (capture_buffer) free(capture_buffer);
    if (playback_buffer) free(playback_buffer);
}

// 清理设备资源
void cleanup_device(audio_device_t *dev) {
    if (dev->handle) {
        // 先停止设备
        if (dev->state == DEVICE_STATE_RUNNING) {
            pcm_stop(dev->handle);
        }
        
        pcm_close(dev->handle);
        dev->handle = NULL;
        dev->state = DEVICE_STATE_CLOSED;
        printf("设备 %s 已关闭\n", dev->device_name);
        
        if (dev->underrun_count > 0) {
            printf("  统计: %d 次缓冲区异常\n", dev->underrun_count);
        }
    }
}

// 清理资源
void cleanup() {
    printf("\n正在清理资源...\n");
    cleanup_device(&capture_dev);
    cleanup_device(&playback_dev);
    printf("资源清理完成\n");
}

// 显示帮助信息
void show_usage(const char *prog_name) {
    printf("用法: %s [选项]\n", prog_name);
    printf("选项:\n");
    printf("  -i, --input <dev>    指定采集设备 (默认: hw:0,0)\n");
    printf("  -o, --output <dev>   指定播放设备 (默认: hw:2,0)\n");
    printf("  -I, --input-ch <n>   指定采集通道数 (默认: %d)\n", DEFAULT_CAPTURE_CHANNELS);
    printf("  -O, --output-ch <n>  指定播放通道数 (默认: %d)\n", DEFAULT_PLAYBACK_CHANNELS);
    printf("  -c, --capture <ch1:ch2>  选择采集通道对 (默认: 0:1)\n");
    printf("  -p, --playback <ch1:ch2> 选择播放通道对 (默认: 0:1)\n");
    printf("  -f, --frame <ms>     设置帧长 (1,4,8ms, 默认: 4)\n");
    printf("  -d, --debug          启用调试模式\n");
    printf("  -l, --list           列出可用的音频设备\n");
    printf("  -h, --help           显示此帮助信息\n");
    printf("\n");
    printf("示例:\n");
    printf("  %s -l                        列出可用设备\n", prog_name);
    printf("  %s                           使用默认设置 (采集0:1 -> 播放0:1)\n", prog_name);
    printf("  %s -i hw:1,0 -o hw:3,0       指定采集和播放设备\n", prog_name);
    printf("  %s -I 8 -O 4 -c 2:3          8通道采集，4通道播放，选择采集通道2:3\n", prog_name);
    printf("  %s -c 2:3 -p 0:1             采集通道2:3 -> 播放通道0:1\n", prog_name);
    printf("  %s -d                        启用调试模式\n", prog_name);
    printf("\n");
    printf("注意: 本程序使用tinyalsa库，适用于嵌入式系统\n");
    printf("故障排除:\n");
    printf("  如果遇到设备错误，请先运行 '%s -l' 查看可用设备\n", prog_name);
    printf("  确保音频设备驱动已正确加载\n");
    printf("  检查设备权限，可能需要root权限或加入audio组\n");
    printf("  如果仍有问题，尝试使用 -d 选项查看详细错误信息\n");
}

int main(int argc, char *argv[]) {
    int opt;
    int list_devices = 0;
    struct option long_options[] = {
        {"input", required_argument, 0, 'i'},
        {"output", required_argument, 0, 'o'},
        {"input-ch", required_argument, 0, 'I'},
        {"output-ch", required_argument, 0, 'O'},
        {"capture", required_argument, 0, 'c'},
        {"playback", required_argument, 0, 'p'},
        {"frame", required_argument, 0, 'f'},
        {"debug", no_argument, 0, 'd'},
        {"list", no_argument, 0, 'l'},
        {"help", no_argument, 0, 'h'},
        {0, 0, 0, 0}
    };

    // 解析命令行参数
    while ((opt = getopt_long(argc, argv, "i:o:I:O:c:p:f:dlh", long_options, NULL)) != -1) {
        switch (opt) {
            case 'i':
                strncpy(capture_device, optarg, sizeof(capture_device) - 1);
                capture_device[sizeof(capture_device) - 1] = '\0';
                break;
            case 'o':
                strncpy(playback_device, optarg, sizeof(playback_device) - 1);
                playback_device[sizeof(playback_device) - 1] = '\0';
                break;
            case 'I':
                capture_channels = atoi(optarg);
                if (capture_channels <= 0 || capture_channels > 64) {
                    fprintf(stderr, "错误: 采集通道数必须在1-64之间\n");
                    return 1;
                }
                break;
            case 'O':
                playback_channels = atoi(optarg);
                if (playback_channels <= 0 || playback_channels > 32) {
                    fprintf(stderr, "错误: 播放通道数必须在1-32之间\n");
                    return 1;
                }
                break;
            case 'c':
                if (parse_channel_pair(optarg, &ch1, &ch2) < 0) {
                    return 1;
                }
                break;
            case 'p':
                if (parse_channel_pair(optarg, &pch1, &pch2) < 0) {
                    return 1;
                }
                break;
            case 'f':
                frame_time = atoi(optarg);
                if (frame_time != 1 && frame_time != 4 && frame_time != 8) {
                    fprintf(stderr, "错误: 帧长必须是 1, 4, 或 8ms\n");
                    return 1;
                }
                break;
            case 'd':
                debug_mode = 1;
                printf("调试模式已启用\n");
                break;
            case 'l':
                list_devices = 1;
                break;
            case 'h':
                show_usage(argv[0]);
                return 0;
            default:
                show_usage(argv[0]);
                return 1;
        }
    }

    // 如果只是列出设备，执行后退出
    if (list_devices) {
        list_audio_devices();
        return 0;
    }

    // 验证采集通道参数
    if (ch1 < 0 || ch1 >= capture_channels) {
        fprintf(stderr, "错误: 采集通道1超出范围 (0-%d)\n", capture_channels-1);
        return 1;
    }
    if (ch2 < 0 || ch2 >= capture_channels) {
        fprintf(stderr, "错误: 采集通道2超出范围 (0-%d)\n", capture_channels-1);
        return 1;
    }

    // 验证播放通道参数
    if (pch1 < 0 || pch1 >= playback_channels) {
        fprintf(stderr, "错误: 播放通道1超出范围 (0-%d)\n", playback_channels-1);
        return 1;
    }
    if (pch2 < 0 || pch2 >= playback_channels) {
        fprintf(stderr, "错误: 播放通道2超出范围 (0-%d)\n", playback_channels-1);
        return 1;
    }

    printf("=== 嵌入式Linux音频测试程序 (tinyalsa) ===\n");
    printf("配置: 采集通道%d,%d -> 播放通道%d,%d, 帧长%dms\n",
           ch1, ch2, pch1, pch2, frame_time);

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 设置高优先级（需要root权限）
    if (set_high_priority() != 0) {
        printf("警告: 无法设置高优先级，继续运行...\n");
    }

    // 检查设备可用性
    printf("检查设备可用性...\n");
    int capture_check = check_device_availability(capture_device, 1, capture_channels);
    int playback_check = check_device_availability(playback_device, 0, playback_channels);

    if (capture_check < 0) {
        fprintf(stderr, "错误: 采集设备 %s 不可用\n", capture_device);
        printf("提示: 运行 '%s -l' 查看可用设备\n", argv[0]);
        return 1;
    }

    if (playback_check < 0) {
        fprintf(stderr, "错误: 播放设备 %s 不可用\n", playback_device);
        printf("提示: 运行 '%s -l' 查看可用设备\n", argv[0]);
        return 1;
    }

    printf("设备检查通过 ✓\n");

    // 注册清理函数
    atexit(cleanup);

    // 初始化音频设备
    printf("\n初始化音频设备...\n");
    if (init_capture_device() != 0) {
        fprintf(stderr, "采集设备初始化失败\n");
        return 1;
    }

    if (init_playback_device() != 0) {
        fprintf(stderr, "播放设备初始化失败\n");
        return 1;
    }

    // 【重要】在这里调用sync_devices，而不是单独调用start_device
    printf("同步启动设备...\n");
    if (sync_devices() != 0) {
        fprintf(stderr, "设备同步启动失败\n");
        return 1;
    }

    printf("\n所有设备启动成功，开始音频处理...\n");

    // 开始音频处理
    audio_loop();

    printf("程序正常退出\n");
    return 0;
}