# 音曼科技 RK3576 Linux 系统开发指南

## 概述

本文档详细介绍了如何构建基于 RK3576 芯片的 Linux 系统，包括代码同步、编译环境配置和各个组件的构建过程。

## 1. 代码同步

### 1.1 从 YM 内部 GitLab 同步代码

请使用以下命令从音曼 Gitlab 服务器同步代码：

```bash
# 从 YM 内部 GitLab 初始化仓库
repo init \
    --repo-url ssh://git@**************:2224/rk3576/repo.git \
    -u ssh://git@**************:2224/rk3576/manifests.git \
    -b ym_rk3576 \
    -m rk3576_ym_audio_linux6.1_release.xml

# 同步代码
.repo/repo/repo sync

# 创建并切换到工作分支
.repo/repo/repo start ym_rk3576 --all
```

**注意：** 使用 YM 内部源需要配置 SSH 密钥访问权限。

外网访问请将 IP 地址和端口替换为：

- IP: ************:9060
- 完整地址：ssh://git@************:9060/rk3576/repo.git

由于带宽限制，外网访问速度比较慢。

### 1.2 从本地代码 tar 包同步代码

为了减少外网同步代码时间，将预先下载好的代码 tar 包放在公司 Windows 服务器：

路径：

```
\\***************\1hardware\芯片选型\主处理器\RK3576\rk3576_ym_audio_repo.tar.gz
```

将 tar 包下载到本地目录，解压后，进入 rk3576_ym_audio 目录，执行如下命令：

```bash
# Checkout代码
.repo/repo/repo sync -l

# 创建并切换到工作分支
.repo/repo/repo start ym_rk3576 --all
```

## 2. 系统构建

### 2.1 完整系统构建

构建完整的系统镜像，包括内核、根文件系统和 U-Boot。

音曼产品配置文件：`rockchip_rk3576_ym_audio_v10_defconfig`

```bash
# 配置构建目标
./build.sh lunch:rockchip_rk3576_ym_audio_v10_defconfig

# 开始构建
./build.sh
```

**注意：** 音曼 Windows 服务器存放了下载好的 buildroot dl 文件，可以将其拷贝到以下目录以加速构建过程：

目标目录：

```
rk3576_linux6.1_release/buildroot/dl
```

服务器路径：

```
\\***************\1hardware\芯片选型\主处理器\RK3576\dl.zip
```

### 2.2 根文件系统构建

如果只需要重新构建根文件系统：

```bash
./build.sh rootfs
```

### 2.3 内核构建

**内核配置文件：**

- `rockchip_linux_defconfig`
- `rk3576.config`

**DTS 文件：**

- `rk3576-ym-audio-v10-linux.dts`

#### 方法一：使用简化命令

```bash
# 配置内核
make ARCH=arm64 rockchip_linux_defconfig rk3576.config

# 构建内核镜像
make ARCH=arm64 rk3576-ym-audio-v10-linux.img -j24
```

#### 方法二：完整的交叉编译命令

```bash
# 配置内核（包含 RK3576 特定配置）
make CROSS_COMPILE=/Path_to_your_sdk_folder/prebuilts/gcc/linux-x86/aarch64/\
    gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- \
    ARCH=arm64 \
    rockchip_linux_defconfig \
    rk3576.config

# 构建内核镜像
make CROSS_COMPILE=/Path_to_your_sdk_folder/prebuilts/gcc/linux-x86/aarch64/\
    gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- \
    ARCH=arm64 \
    rk3576-ym-audio-v10-linux.img
```

### 2.4 U-Boot 构建

构建 U-Boot 引导加载程序：

```bash
./make.sh CROSS_COMPILE=/Path_to_your_sdk_folder/prebuilts/gcc/linux-x86/aarch64/\
    gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- \
    rk3576 --spl-new
```

### 2.5 系统存储分区表

| 序号 | 分区名称 | 起始扇区 | 扇区数量 | 实际大小 | 功能描述 |
|------|----------|----------|----------|------------|----------|
| 1 | uboot | 0x00004000 | 0x00002000 | 4MB | U-Boot 引导程序 |
| 2 | misc | 0x00006000 | 0x00002000 | 4MB | 杂项数据分区 |
| 3 | boot | 0x00008000 | 0x00020000 | 64MB | Linux 内核启动分区 |
| 4 | recovery | 0x00028000 | 0x00040000 | 128MB | 恢复模式分区 |
| 5 | backup | 0x00068000 | 0x00010000 | 32MB | 备份数据分区 |
| 6 | rootfs | 0x00078000 | 0x00100000 | 512MB | 根文件系统分区 |
| 7 | app | 0x00178000 | 0x00040000 | 128MB | 应用程序分区 |
| 8 | appcfg | 0x001B8000 | 0x00040000 | 128MB | 应用配置分区 |
| 9 | appbak | 0x001F8000 | 0x00040000 | 128MB | 应用备份分区 |
| 10 | ota | 0x00238000 | 0x00100000 | 512MB | OTA 升级分区 |
| 11 | userdata | 0x00338000 | - | 剩余空间 | 用户数据分区（可增长） |

## 3. 调试记录

本节记录了在 RK3576 音频系统开发过程中的各种调试方法和测试命令。

### 3.1 OTA 升级

重启进入 loader 模式：

```bash
reboot loader
```

### 3.2 Line In/Out Codec 调试

测试 NAL88L21 音频编解码器，采用标准alsaloop命令进行测试：

```bash
# 音频环回测试
alsaloop -C hw:2,0 -P hw:2,0 -c 2 -r 48000 -f S32_LE -t 50000 -v

alsaloop -C hw:3,0 -P hw:3,0 -c 2 -r 48000 -f S32_LE -t 50000 -v

# NAL88L21寄存器为16bit，i2ctransfer 进行寄存器读取命令
i2ctransfer -y -f 2 w2@0x54 0x00 0x00 r2
```

### 3.3 32 麦克风阵列调试

测试 ES7210 多通道音频 ADC，采用arecord标准工具进行录音测试，注意，录音格式为48000采样率，32bit，32通道时，EMMC写入速度可能跟不上，从而导致缓冲区溢出错误，常用的一些测试命令如下：

```bash
# 查看硬件参数
arecord -D hw:0,0 --dump-hw-params < /dev/zero 2>&1

# 录音测试
arecord -D hw:0,0 -c 32 -f S32_LE -d 10 -r 48000 -t wav /userdata/sink.wav

# 查看ES7210 I2C 设备寄存器值
i2cdump -y -f 4 0x40

# 优化缓冲区录音
arecord -v -D hw:0,0 -c 32 --buffer-size=8192 -f S32_LE -r 48000 -t wav /userdata/sink.wav
```

### 3.4 ALSA 音频测试程序

编译 alsa_c2p_test 进行音频通路测试，具体可参考代码仓库中的 README.md 文件，主要实现从输入设备采集音频数据，选择特定通道，然后输出到指定的播放设备：

```bash
./alsa_c2p_test -i hw:0,0 -o hw:2,0 -I 32 -O 2 -c 16:16 -p 0:1 -f 4 -m
```

**参数说明：**

- `-i hw:0,0`: 输入设备
- `-o hw:2,0`: 输出设备
- `-I 32`: 输入通道数
- `-O 2`: 输出通道数
- `-c 16:16`: 通道映射
- `-p 0:1`: 端口映射
- `-f 4`: 格式设置
- `-m`: 监控模式

### 3.5 红外遥控调试

红外遥控器调试方法：

```bash
# 开启调试信息
echo 1 > /sys/module/rockchip_pwm_remotectl/parameters/dbg_level
echo 1 > /sys/module/rockchip_pwm_remotectl/parameters/code_print

# 测试输入事件命令
evtest
```

### 3.6 网络调试

**网卡配置说明：**

- 网卡时钟由 RK3576 提供，节省晶振芯片成本

### 3.7 系统优化

**Buildroot 文件系统优化：**

- rootfs 格式从 ext4 改为 squashfs
- 系统大小从 1GB 裁剪至 190MB

## 4. 构建环境要求

### 4.1 硬件要求

- 推荐使用多核处理器（命令中使用了 `-j24` 参数）
- 足够的磁盘空间（建议至少 100GB）
- 充足的内存（建议至少 8GB）

### 4.2 软件依赖

- Linux 开发环境（推荐 Ubuntu 20.04 或更高版本）
- Git 和 repo 工具
- 交叉编译工具链（已包含在代码仓库中）
- 必要的构建工具（make, gcc, python 等）

## 5. 目标平台信息

- **芯片平台：** RK3576
- **开发板：** EVB1 v1.0
- **架构：** ARM64
- **Linux 内核版本：** 6.1
- **配置文件：** rockchip_rk3576_ym_audio_v10_defconfig

## 6. 注意事项

1. **网络访问：** 确保能够访问相应的 Git 仓库
2. **权限配置：** 使用 SSH 方式同步代码时需要配置相应的访问权限
3. **路径设置：** 交叉编译工具链的路径可能需要根据实际安装位置调整
4. **并行编译：** 可以根据主机性能调整 `-j` 参数的数值
5. **清理构建：** 如果遇到构建问题，可能需要清理之前的构建产物

## 7. 故障排除

### 常见问题

- **同步失败：** 检查网络连接和仓库访问权限
- **编译错误：** 确认依赖包是否完整安装
- **路径问题：** 检查交叉编译工具链路径是否正确

### 获取帮助

遇到问题时，建议查看构建日志的详细输出，并根据错误信息进行相应的调试。
