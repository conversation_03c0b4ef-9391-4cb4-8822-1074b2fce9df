# tinyalsa音频采集播放测试程序

## 概述

`tinyalsa_c2p_test.c` 是一个专门使用tinyalsa库实现的音频采集播放测试程序，功能与 `alsa_c2p_test.c` 相同，但更适合嵌入式系统使用。

## 编译

### 标准编译
```bash
make tinyalsa_c2p_test
```

### 交叉编译
```bash
make CROSS=1 tinyalsa_c2p_test
```

### 查看所有编译选项
```bash
make help
```

## 使用方法

### 基本语法
```bash
./tinyalsa_c2p_test [选项]
```

### 命令行参数

| 参数 | 长参数 | 说明 | 默认值 |
|------|--------|------|--------|
| -i | --input | 指定采集设备 | hw:0,0 |
| -o | --output | 指定播放设备 | hw:2,0 |
| -I | --input-ch | 指定采集通道数 (1-64) | 32 |
| -O | --output-ch | 指定播放通道数 (1-32) | 2 |
| -c | --capture | 选择采集通道对 (格式: ch1:ch2) | 0:1 |
| -p | --playback | 选择播放通道对 (格式: ch1:ch2) | 0:1 |
| -f | --frame | 设置帧长(1,4,8ms) | 4 |
| -d | --debug | 启用调试模式 | 关闭 |
| -l | --list | 列出可用的音频设备 | - |
| -h | --help | 显示帮助信息 | - |

### 使用示例

1. **列出可用设备**
   ```bash
   ./tinyalsa_c2p_test -l
   ```

2. **基本使用**
   ```bash
   ./tinyalsa_c2p_test
   ```
   采集通道0:1 → 播放通道0:1

3. **指定采集和播放设备**
   ```bash
   ./tinyalsa_c2p_test -i hw:1,0 -o hw:3,0
   ```

4. **设置通道配置**
   ```bash
   ./tinyalsa_c2p_test -I 8 -O 4 -c 2:3
   ```
   - 8通道采集，4通道播放
   - 选择采集通道2:3 → 播放通道0:1

5. **灵活的通道映射**
   ```bash
   ./tinyalsa_c2p_test -c 2:3 -p 0:1
   ```
   采集通道2:3 → 播放通道0:1
   
   ```bash
   ./tinyalsa_c2p_test -c 5:7 -p 2:3
   ```
   采集通道5:7 → 播放通道2:3

6. **声道交换**
   ```bash
   ./tinyalsa_c2p_test -c 0:1 -p 1:0
   ```
   左右声道交换 (采集0→播放1, 采集1→播放0)

7. **单声道复制**
   ```bash
   ./tinyalsa_c2p_test -c 3:3 -p 0:2
   ```
   采集通道3 → 播放通道0和2

8. **启用调试模式**
   ```bash
   ./tinyalsa_c2p_test -d
   ```

9. **多通道播放设备**
   ```bash
   ./tinyalsa_c2p_test -O 8 -c 0:1 -p 4:5
   ```
   8通道播放，采集0:1 → 播放4:5

10. **完整配置示例**
    ```bash
    ./tinyalsa_c2p_test -i hw:1,0 -o hw:2,0 \
                        -I 16 -O 8 \
                        -c 4:5 -p 2:3 \
                        -f 8 -d
    ```

## 功能特性

- **轻量级**: 使用tinyalsa库，内存占用小
- **嵌入式优化**: 专为嵌入式系统设计
- **灵活的设备配置**: 可以指定任意的音频采集和播放设备
- **可配置通道数**: 支持1-64个采集通道，1-32个播放通道
- **灵活的通道映射**: 支持任意采集通道到播放通道的映射
- **声道处理**: 支持左右声道交换、单声道复制等功能
- **实时处理**: 支持1ms、4ms、8ms的帧长设置
- **设备检测**: 自动检测和列出可用的音频设备
- **实时优先级**: 自动设置进程实时优先级以获得更好性能
- **错误恢复**: 自动处理缓冲区溢出/下溢等错误
- **调试模式**: 提供详细的运行信息和信号强度监控

## 技术参数

- **采样率**: 48000 Hz (固定)
- **采样格式**: S32_LE (32位小端序)
- **支持的帧长**: 1ms、4ms、8ms
- **最大采集通道**: 64通道
- **最大播放通道**: 32通道
- **访问模式**: 标准读写模式（不支持MMAP）

## 与ALSA版本的对比

| 功能 | ALSA版本 | tinyalsa版本 |
|------|----------|-------------|
| 基本音频采集播放 | ✓ | ✓ |
| 多通道支持 | ✓ | ✓ |
| 通道映射 | ✓ | ✓ |
| MMAP模式 | ✓ | ✗ |
| 内存占用 | 较大 | 较小 |
| 库依赖 | libasound | libtinyalsa |
| 适用场景 | 桌面/服务器 | 嵌入式 |
| 配置复杂度 | 较高 | 较低 |

## 注意事项

1. **权限要求**: 程序会尝试设置实时优先级，需要root权限或适当的权限配置
2. **设备存在性**: 确保指定的音频设备存在且可访问，可使用 `-l` 参数检查
3. **通道范围**: 选择的通道号必须在采集/播放设备支持的范围内
4. **系统负载**: 实时音频处理对系统性能有一定要求
5. **通道格式**: 通道参数使用 `ch1:ch2` 格式，例如 `0:1` 或 `2:3`
6. **设备命名**: 设备名称格式为 `hw:card,device`，例如 `hw:0,0` 或 `hw:1,0`
7. **库依赖**: 需要系统安装libtinyalsa库
8. **弃用警告**: 编译时可能出现pcm_read/pcm_write函数弃用警告，这不影响功能

## 故障排除

1. **设备不存在**
   ```bash
   ./tinyalsa_c2p_test -l  # 查看可用设备
   ```

2. **权限问题**
   - 使用root权限运行
   - 将用户加入audio组: `sudo usermod -a -G audio $USER`

3. **库依赖问题**
   ```bash
   # 检查tinyalsa库是否安装
   ldconfig -p | grep tinyalsa
   ```

4. **性能问题**
   - 增大帧长: `-f 8`
   - 检查系统负载
   - 确保实时优先级设置成功

5. **通道错误**
   - 确保通道号在有效范围内
   - 使用 `-d` 调试模式查看详细信息

## 退出程序

使用 Ctrl+C 或发送 SIGTERM 信号来安全退出程序。

## 编译状态

- ✅ 编译成功
- ⚠️ 有弃用函数警告（不影响功能）
- ✅ 支持交叉编译
- ✅ 功能完整
