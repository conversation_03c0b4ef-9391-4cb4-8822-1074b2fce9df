#!/bin/bash

# 测试 pcm_readi 时间问题的脚本

echo "=== 测试 pcm_readi 时间问题 ==="

# 编译程序
echo "编译 tinyalsa_record..."
gcc -o tinyalsa_record tinyalsa_record.c -ltinyalsa -lm

if [ $? -ne 0 ]; then
    echo "编译失败，请检查 tinyalsa 库是否安装"
    exit 1
fi

echo "编译成功"

# 编译WAV分析工具
echo "编译 wav_analyzer..."
gcc -o wav_analyzer wav_analyzer.c

if [ $? -ne 0 ]; then
    echo "编译 wav_analyzer 失败"
    exit 1
fi

echo "WAV分析工具编译成功"

# 测试函数
test_recording() {
    local duration=$1
    local filename="/tmp/test_${duration}sec.wav"
    
    echo ""
    echo "=== 测试录音 ${duration} 秒 ==="
    
    # 记录开始时间
    start_time=$(date +%s.%N)
    
    # 执行录音
    ./tinyalsa_record -t $duration -f $filename -D
    
    # 记录结束时间
    end_time=$(date +%s.%N)
    
    # 计算实际耗时
    actual_time=$(echo "$end_time - $start_time" | bc -l)
    
    echo ""
    echo "时间分析:"
    printf "  请求录音时长: %d 秒\n" $duration
    printf "  实际程序耗时: %.3f 秒\n" $actual_time
    
    if [ -f $filename ]; then
        # 获取文件大小
        file_size=$(stat -c%s $filename)
        
        # 分析WAV文件
        echo ""
        echo "WAV文件分析:"
        ./wav_analyzer $filename
        
        # 计算期望文件大小 (假设48kHz, 2通道, 32位)
        expected_size=$(( duration * 48000 * 2 * 4 + 44 ))
        
        echo ""
        echo "文件大小分析:"
        echo "  实际文件大小: $file_size bytes"
        echo "  期望文件大小: $expected_size bytes"
        echo "  大小差异: $(( file_size - expected_size )) bytes"
        
        # 计算时长差异
        time_diff=$(echo "$actual_time - $duration" | bc -l)
        printf "  时长差异: %.3f 秒\n" $time_diff
        
        # 判断是否存在问题
        if (( $(echo "$time_diff > 0.5" | bc -l) )); then
            echo "  ❌ 实际耗时明显超过请求时长"
        elif (( $(echo "$time_diff < -0.5" | bc -l) )); then
            echo "  ❌ 实际耗时明显少于请求时长 - 可能存在pcm_readi快速返回问题"
        else
            echo "  ✅ 时长基本正确"
        fi
        
        # 检查文件大小
        size_diff_percent=$(echo "scale=2; ($file_size - $expected_size) * 100 / $expected_size" | bc -l)
        if (( $(echo "${size_diff_percent#-} > 5" | bc -l) )); then
            printf "  ❌ 文件大小差异过大: %.1f%%\n" $size_diff_percent
        else
            printf "  ✅ 文件大小基本正确 (差异: %.1f%%)\n" $size_diff_percent
        fi
        
    else
        echo "  ❌ 录音文件未生成"
    fi
    
    # 清理文件
    rm -f $filename
}

# 检查bc命令是否可用
if ! command -v bc &> /dev/null; then
    echo "警告: bc 命令不可用，将跳过精确计算"
    BC_AVAILABLE=false
else
    BC_AVAILABLE=true
fi

# 测试不同时长
test_recording 3
test_recording 5
test_recording 10

echo ""
echo "=== 测试总结 ==="
echo "如果发现以下问题，说明存在 pcm_readi 时间问题:"
echo "1. 实际程序耗时明显少于请求时长"
echo "2. 文件大小正确但时长显示错误"
echo "3. 调试信息显示读取时间过短"
echo ""
echo "解决方案:"
echo "1. 清空PCM缓冲区中的旧数据"
echo "2. 使用帧数而非墙钟时间计算录音时长"
echo "3. 验证每次读取的数据是否为新数据"

echo ""
echo "测试完成"
