# RK3308 系统存储分区表

## 分区概述
基于 MTD (Memory Technology Device) 分区布局，以下是 RK3308 系统的完整存储分区表：

## 分区详细信息

| 序号 | 分区名称 | 起始扇区 | 扇区数量 | 实际大小 | 功能描述 |
|------|----------|----------|----------|------------|----------|
| 1 | uboot | 0x00004000 | 0x00002000 | 4MB | U-Boot 引导程序 |
| 2 | misc | 0x00006000 | 0x00002000 | 4MB | 杂项数据分区 |
| 3 | boot | 0x00008000 | 0x00020000 | 64MB | Linux 内核启动分区 |
| 4 | recovery | 0x00028000 | 0x00040000 | 128MB | 恢复模式分区 |
| 5 | backup | 0x00068000 | 0x00010000 | 32MB | 备份数据分区 |
| 6 | rootfs | 0x00078000 | 0x00100000 | 512MB | 根文件系统分区 |
| 7 | app | 0x00178000 | 0x00040000 | 128MB | 应用程序分区 |
| 8 | appcfg | 0x001B8000 | 0x00040000 | 128MB | 应用配置分区 |
| 9 | appbak | 0x001F8000 | 0x00040000 | 128MB | 应用备份分区 |
| 10 | ota | 0x00238000 | 0x00100000 | 512MB | OTA 升级分区 |
| 11 | userdata | 0x00338000 | - | 剩余空间 | 用户数据分区（可增长） |

## 地址空间分布图

```
扇区0x00000000  ┌─────────────────┐
                │   保留区域      │
扇区0x00004000  ├─────────────────┤
                │  uboot (4MB)    │
扇区0x00006000  ├─────────────────┤
                │  misc (4MB)     │
扇区0x00008000  ├─────────────────┤
                │  boot (64MB)    │
扇区0x00028000  ├─────────────────┤
                │recovery (128MB) │
扇区0x00068000  ├─────────────────┤
                │ backup (32MB)   │
扇区0x00078000  ├─────────────────┤
                │ rootfs (512MB)  │
扇区0x00178000  ├─────────────────┤
                │  app (128MB)    │
扇区0x001B8000  ├─────────────────┤
                │ appcfg (128MB)  │
扇区0x001F8000  ├─────────────────┤
                │ appbak (128MB)  │
扇区0x00238000  ├─────────────────┤
                │  ota (512MB)    │
扇区0x00338000  ├─────────────────┤
                │ userdata (grow) │
                │                 │
                └─────────────────┘
```

## 分区功能说明

### 系统引导分区
- **uboot**: U-Boot 引导加载程序，负责系统初始化和内核加载
- **misc**: 存储系统杂项信息，如启动模式标志
- **boot**: Linux 内核镜像存储区域

### 系统恢复分区
- **recovery**: 恢复模式内核和文件系统，用于系统修复
- **backup**: 系统关键数据备份

### 文件系统分区
- **rootfs**: 根文件系统，包含系统核心文件和库

### 应用程序分区
- **app**: 用户应用程序存储区域
- **appcfg**: 应用程序配置文件存储
- **appbak**: 应用程序备份数据

### 升级和用户数据分区
- **ota**: Over-The-Air 升级包存储区域
- **userdata**: 用户数据存储，采用可增长设计，占用剩余存储空间

## 技术特性

1. **MTD 设备**: 使用 Memory Technology Device 驱动管理 Flash 存储
2. **扇区计算**: 每个扇区 512 字节，分区大小 = 扇区数量 × 512 字节
3. **固定分区**: 前 10 个分区大小固定，确保系统稳定性
4. **动态分区**: userdata 分区可根据剩余空间动态增长
5. **冗余设计**: 包含 backup、appbak 等备份分区，提高系统可靠性

## 大小计算说明

- **计算公式**: 实际大小 = 扇区数量 × 512 字节
- **示例**: 0x00002000 = 8192 个扇区 = 8192 × 512 = 4,194,304 字节 = 4MB
- **十六进制转换**: 0x00002000 = 8192 (十进制)

## 注意事项

- 分区表基于十六进制地址定义
- userdata 分区使用 "grow" 标志，自动占用剩余存储空间
- 修改分区表需要重新烧录系统镜像
- 各分区大小已针对 RK3308 音频应用优化

---
*生成时间: 2025-09-02*
*基于文件: w:\home\bqin\work\rk3308\audio_test\readme*