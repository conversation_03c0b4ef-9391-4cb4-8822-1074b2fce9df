# ALSA音频采集播放测试程序

## 概述
这是一个基于ALSA的音频采集播放测试程序，可以从指定的采集设备读取音频数据，选择特定通道，然后输出到指定的播放设备。

## 编译

### 使用标准ALSA库编译
```bash
gcc -o alsa_c2p_test alsa_c2p_test.c -lasound -lpthread
```

或者使用项目提供的Makefile：
```bash
make
```

### 使用tinyalsa库编译
```bash
gcc -DUSE_TINYALSA -o alsa_c2p_test alsa_c2p_test.c -lasound -ltinyalsa -lpthread
```

或者使用Makefile：
```bash
make USE_TINYALSA=1
```

### 交叉编译（支持tinyalsa）
```bash
make CROSS=1 USE_TINYALSA=1 CROSS_COMPILE=aarch64-linux-gnu-
```

## 使用方法

### 基本语法
```bash
./alsa_c2p_test [选项]
```

### 命令行参数

| 参数 | 长参数 | 说明 | 默认值 |
|------|--------|------|--------|
| -i | --input | 指定采集设备 | hw:0,0 |
| -o | --output | 指定播放设备 | hw:2,0 |
| -I | --input-ch | 指定采集通道数 (1-64) | 32 |
| -O | --output-ch | 指定播放通道数 (1-32) | 2 |
| -c | --capture | 选择采集通道对 (格式: ch1:ch2) | 0:1 |
| -p | --playback | 选择播放通道对 (格式: ch1:ch2) | 0:1 |
| -f | --frame | 设置帧长(1,4,8ms) | 4 |
| -m | --mmap | 使用MMAP模式 (零拷贝，更低延迟) | 关闭 |
| -t | --tinyalsa | 使用tinyalsa库 (更轻量级) | 关闭 |
| -d | --debug | 启用调试模式 | 关闭 |
| -l | --list | 列出可用的音频设备 | - |
| -h | --help | 显示帮助信息 | - |

**注意**: `-t/--tinyalsa` 参数仅在编译时启用了 `USE_TINYALSA` 宏定义时可用。

### 使用示例

1. **列出可用设备**
   ```bash
   ./alsa_c2p_test -l
   ```

2. **使用默认设置**
   ```bash
   ./alsa_c2p_test
   ```
   采集通道0:1 → 播放通道0:1

3. **指定采集和播放设备**
   ```bash
   ./alsa_c2p_test -i hw:1,0 -o hw:3,0
   ```

4. **设置通道配置**
   ```bash
   ./alsa_c2p_test -I 8 -O 4 -c 2:3
   ```
   - 8通道采集，4通道播放
   - 选择采集通道2:3 → 播放通道0:1

5. **灵活的通道映射**
   ```bash
   ./alsa_c2p_test -c 2:3 -p 0:1
   ```
   采集通道2:3 → 播放通道0:1
   
   ```bash
   ./alsa_c2p_test -c 5:7 -p 2:3
   ```
   采集通道5:7 → 播放通道2:3

6. **声道交换**
   ```bash
   ./alsa_c2p_test -c 0:1 -p 1:0
   ```
   左右声道交换 (采集0→播放1, 采集1→播放0)

7. **单声道复制**
   ```bash
   ./alsa_c2p_test -c 3:3 -p 0:2
   ```
   采集通道3 → 播放通道0和2

8. **使用MMAP模式和调试**
   ```bash
   ./alsa_c2p_test -m -d
   ```

9. **使用tinyalsa库**
   ```bash
   ./alsa_c2p_test -t -d
   ```
   使用tinyalsa库，启用调试

   ```bash
   ./alsa_c2p_test -t -i hw:1,0 -o hw:2,0
   ```
   使用tinyalsa，指定设备

10. **多通道播放设备**
    ```bash
    ./alsa_c2p_test -O 8 -c 0:1 -p 4:5
    ```
    8通道播放，采集0:1 → 播放4:5

11. **完整配置示例**
    ```bash
    ./alsa_c2p_test -i hw:1,0 -o hw:2,0 \
                     -I 16 -O 2 -c 4:5 -f 4 -d
    ```

## 功能特性

- **灵活的设备配置**: 可以指定任意的ALSA采集和播放设备
- **可配置通道数**: 支持1-64个采集通道，1-32个播放通道
- **灵活的通道映射**: 支持任意采集通道到播放通道的映射
- **声道处理**: 支持左右声道交换、单声道复制等功能
- **实时处理**: 支持1ms、4ms、8ms的帧长设置
- **MMAP模式**: 零拷贝模式，提供更低延迟（仅标准ALSA）
- **tinyalsa支持**: 轻量级音频库，适用于嵌入式系统
- **设备检测**: 自动检测和列出可用的音频设备
- **实时优先级**: 自动设置进程实时优先级以获得更好性能
- **错误恢复**: 自动处理缓冲区溢出/下溢等错误
- **调试模式**: 提供详细的运行信息和信号强度监控

## 技术参数

- **采样率**: 48000 Hz (固定)
- **采样格式**: S32_LE (32位小端序)
- **支持的帧长**: 1ms、4ms、8ms
- **最大采集通道**: 64通道
- **最大播放通道**: 32通道
- **访问模式**:
  - 标准ALSA: 支持标准读写和MMAP两种模式
  - tinyalsa: 仅支持标准读写模式

## 注意事项

1. **权限要求**: 程序会尝试设置实时优先级，需要root权限或适当的权限配置
2. **设备存在性**: 确保指定的音频设备存在且可访问，可使用 `-l` 参数检查
3. **通道范围**: 选择的通道号必须在采集/播放设备支持的范围内
4. **系统负载**: 实时音频处理对系统性能有一定要求，MMAP模式性能更佳
5. **通道格式**: 通道参数使用 `ch1:ch2` 格式，例如 `0:1` 或 `2:3`
6. **设备命名**: 设备名称格式为 `hw:card,device`，例如 `hw:0,0` 或 `hw:1,0`
7. **库选择**:
   - **标准ALSA**: 功能完整，支持MMAP模式，适用于桌面和服务器系统
   - **tinyalsa**: 轻量级，内存占用小，适用于嵌入式系统，但不支持MMAP模式
8. **编译依赖**: 使用tinyalsa需要系统安装libtinyalsa-dev包

## 错误处理

程序会自动处理以下错误情况：
- 音频设备打开失败（提供详细的错误信息和解决建议）
- 缓冲区溢出/下溢（自动恢复设备状态）
- 参数范围错误（启动前验证所有参数）
- 设备配置错误（提供设备检查功能）
- 设备权限问题（提供权限相关的提示信息）
- MMAP访问错误（自动降级处理）

## 故障排除

1. **设备不存在**
   ```bash
   ./alsa_c2p_test -l  # 查看可用设备
   arecord -l          # 查看录音设备
   aplay -l            # 查看播放设备
   ```

2. **权限问题**
   - 使用root权限运行
   - 将用户加入audio组: `sudo usermod -a -G audio $USER`

3. **设备被占用**
   ```bash
   lsof /dev/snd/*     # 查看占用设备的进程
   fuser -v /dev/snd/* # 查看设备使用情况
   ```

4. **性能问题**
   - 使用MMAP模式: `-m`
   - 增大帧长: `-f 8`
   - 检查系统负载

5. **通道错误**
   - 确保通道号在有效范围内
   - 使用 `-d` 调试模式查看详细信息

## 退出程序

使用 Ctrl+C 或发送 SIGTERM 信号来安全退出程序。
