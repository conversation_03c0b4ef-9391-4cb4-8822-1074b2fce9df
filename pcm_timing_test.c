#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <unistd.h>
#include <tinyalsa/asoundlib.h>

#define SAMPLE_RATE 48000
#define CHANNELS 2
#define PERIOD_SIZE 192
#define PERIOD_COUNT 4

int main() {
    printf("=== PCM读取时间测试 ===\n");
    
    struct pcm_config config = {
        .channels = CHANNELS,
        .rate = SAMPLE_RATE,
        .period_size = PERIOD_SIZE,
        .period_count = PERIOD_COUNT,
        .format = PCM_FORMAT_S32_LE,
        .start_threshold = PERIOD_SIZE,
        .stop_threshold = PERIOD_SIZE * PERIOD_COUNT,
        .silence_threshold = 0,
    };
    
    // 打开PCM设备
    struct pcm *pcm = pcm_open(2, 0, PCM_IN, &config);
    if (!pcm || !pcm_is_ready(pcm)) {
        fprintf(stderr, "无法打开PCM设备: %s\n", 
                pcm ? pcm_get_error(pcm) : "设备创建失败");
        if (pcm) pcm_close(pcm);
        return 1;
    }
    
    printf("PCM设备已打开\n");
    printf("配置: %d通道, %dHz, period_size=%d\n", CHANNELS, SAMPLE_RATE, PERIOD_SIZE);
    
    // 分配缓冲区
    size_t buffer_size = PERIOD_SIZE * CHANNELS * 4; // S32_LE = 4字节
    void *buffer = malloc(buffer_size);
    if (!buffer) {
        fprintf(stderr, "无法分配缓冲区\n");
        pcm_close(pcm);
        return 1;
    }
    
    printf("\n开始测试 pcm_readi 的时间行为...\n");
    printf("期望每次读取时间: %.3f ms\n", (double)PERIOD_SIZE / SAMPLE_RATE * 1000);
    
    struct timespec start, end;
    double total_time = 0;
    int total_frames = 0;
    
    // 测试20次读取
    for (int i = 0; i < 20; i++) {
        clock_gettime(CLOCK_MONOTONIC, &start);
        
        int frames_read = pcm_readi(pcm, buffer, PERIOD_SIZE);
        
        clock_gettime(CLOCK_MONOTONIC, &end);
        
        double read_time = (end.tv_sec - start.tv_sec) + 
                          (end.tv_nsec - start.tv_nsec) / 1000000000.0;
        
        if (frames_read > 0) {
            total_time += read_time;
            total_frames += frames_read;
            
            double expected_time = (double)frames_read / SAMPLE_RATE;
            
            printf("读取 %d: %d帧, 耗时 %.3f ms (期望 %.3f ms), 比率 %.2f\n",
                   i + 1, frames_read, read_time * 1000, expected_time * 1000,
                   read_time / expected_time);
            
            // 如果读取时间明显小于期望时间，标记为可疑
            if (read_time < expected_time * 0.1) {
                printf("  ⚠️  读取时间过短，可能读取了缓冲区旧数据\n");
            }
        } else {
            printf("读取 %d: 错误 - %s\n", i + 1, pcm_get_error(pcm));
        }
        
        // 短暂延迟
        usleep(1000);
    }
    
    printf("\n=== 统计结果 ===\n");
    printf("总读取帧数: %d\n", total_frames);
    printf("总耗时: %.3f 秒\n", total_time);
    printf("平均每帧耗时: %.6f 秒\n", total_time / total_frames);
    printf("期望每帧耗时: %.6f 秒\n", 1.0 / SAMPLE_RATE);
    
    double time_ratio = (total_time / total_frames) / (1.0 / SAMPLE_RATE);
    printf("时间比率: %.3f\n", time_ratio);
    
    if (time_ratio < 0.5) {
        printf("结论: pcm_readi 返回过快，可能读取了缓冲区旧数据\n");
        printf("建议: 在正式录音前清空缓冲区\n");
    } else if (time_ratio > 2.0) {
        printf("结论: pcm_readi 返回过慢，可能存在性能问题\n");
    } else {
        printf("结论: pcm_readi 时间行为正常\n");
    }
    
    // 清理
    free(buffer);
    pcm_close(pcm);
    
    return 0;
}
